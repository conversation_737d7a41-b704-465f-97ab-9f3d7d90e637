'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { 
  Loader2, 
  RefreshCw, 
  Calendar, 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  Clock,
  Activity,
  TrendingUp
} from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line } from 'recharts';
import {
  CalendarHealthService,
  TokenHealthMetrics,
  ProviderHealth,
  TenantHealth,
  ErrorTrend,
  ExpirationData
} from '@/lib/services/calendar-health-service';

export default function CalendarHealthPage() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // State for different metrics
  const [tokenHealth, setTokenHealth] = useState<TokenHealthMetrics>({
    active: 0,
    expired: 0,
    expiring_soon: 0,
    invalid: 0
  });

  const [providerHealth, setProviderHealth] = useState<ProviderHealth[]>([]);
  const [tenantHealth, setTenantHealth] = useState<TenantHealth[]>([]);
  const [errorTrends, setErrorTrends] = useState<ErrorTrend[]>([]);
  const [expirationTimeline, setExpirationTimeline] = useState<ExpirationData[]>([]);

  // Get service instance
  const calendarHealthService = CalendarHealthService.getInstance();

  useEffect(() => {
    fetchCalendarHealth();
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchCalendarHealth, 30000);
    return () => clearInterval(interval);
  }, []);

  async function fetchCalendarHealth() {
    try {
      setLoading(true);

      // Fetch all health data using the service
      const healthData = await calendarHealthService.fetchCalendarHealth();

      // Update state with fetched data
      setTokenHealth(healthData.tokenHealth);
      setProviderHealth(healthData.providerHealth);
      setTenantHealth(healthData.tenantHealth);
      setErrorTrends(healthData.errorTrends);
      setExpirationTimeline(healthData.expirationTimeline);

    } catch (error: any) {
      console.error('Error fetching calendar health:', error);
      toast({
        title: 'Error',
        description: 'Failed to load calendar health data',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }



  async function handleRefresh() {
    setRefreshing(true);
    await fetchCalendarHealth();
  }

  function getStatusIcon(status: string) {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'degraded':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  }



  return (
    <div className="container mx-auto py-10">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Calendar className="h-8 w-8" />
            Calendar Health Monitoring
          </h1>
          <p className="text-gray-500 mt-1">
            Monitor calendar integration health and token status across all tenants
          </p>
        </div>
        <Button 
          onClick={handleRefresh} 
          disabled={refreshing}
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Token Health Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Tokens</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loading ? <Loader2 className="h-6 w-6 animate-spin" /> : tokenHealth.active}</div>
            <p className="text-xs text-green-600">🟢 Good</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expired Tokens</CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loading ? <Loader2 className="h-6 w-6 animate-spin" /> : tokenHealth.expired}</div>
            <p className="text-xs text-red-600">🔴 Alert</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expiring Soon</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loading ? <Loader2 className="h-6 w-6 animate-spin" /> : tokenHealth.expiring_soon}</div>
            <p className="text-xs text-yellow-600">🟡 Warning</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Invalid Tokens</CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loading ? <Loader2 className="h-6 w-6 animate-spin" /> : tokenHealth.invalid}</div>
            <p className="text-xs text-red-600">🔴 Error</p>
          </CardContent>
        </Card>
      </div>

      {/* Provider Health and Timeline Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Provider Health */}
        <Card>
          <CardHeader>
            <CardTitle>Provider Health</CardTitle>
            <CardDescription>Status of calendar provider integrations</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center h-48">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : (
              <div className="space-y-4">
                {providerHealth.map((provider) => (
                  <div key={provider.provider} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(provider.status)}
                      <div>
                        <div className="font-medium">{provider.provider} Calendar</div>
                        <div className="text-sm text-gray-500">
                          • {provider.active_tokens} active tokens
                        </div>
                        <div className="text-sm text-gray-500">
                          • {provider.uptime.toFixed(1)}% uptime
                        </div>
                        <div className="text-sm text-gray-500">
                          • {provider.avg_response_time}ms avg response
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-sm font-medium ${
                        provider.status === 'healthy' ? 'text-green-600' :
                        provider.status === 'degraded' ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {provider.status === 'healthy' ? '🟢 Healthy' :
                         provider.status === 'degraded' ? '🟡 Degraded' : '🔴 Error'}
                      </div>
                      {provider.last_error && (
                        <div className="text-xs text-red-500 mt-1">{provider.last_error}</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Token Expiration Timeline */}
        <Card>
          <CardHeader>
            <CardTitle>Token Expiration Timeline</CardTitle>
            <CardDescription>Upcoming token expirations over next 30 days</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center h-48">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : (
              <div className="h-48 flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <TrendingUp className="h-8 w-8 mx-auto mb-2" />
                  <p>Timeline chart will be implemented</p>
                  <p className="text-sm">with expiration distribution</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Error Rate Trends Chart */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Error Rate Trends (Last 24h)</CardTitle>
          <CardDescription>Error rates by provider over time</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={errorTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tickFormatter={(value) => new Date(value).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={(value) => new Date(value).toLocaleString()}
                  />
                  <Legend />
                  <Line type="monotone" dataKey="google_errors" stroke="#8884d8" name="Google" />
                  <Line type="monotone" dataKey="microsoft_errors" stroke="#82ca9d" name="Microsoft" />
                  <Line type="monotone" dataKey="calendly_errors" stroke="#ffc658" name="Calendly" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tenant Health Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Tenant Health Summary</CardTitle>
          <CardDescription>Calendar integration health by tenant</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tenant Name</TableHead>
                  <TableHead>Health Score</TableHead>
                  <TableHead>Providers Connected</TableHead>
                  <TableHead>Last Sync</TableHead>
                  <TableHead>Issues</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tenantHealth.map((tenant) => (
                  <TableRow key={tenant.tenant_id}>
                    <TableCell className="font-medium">{tenant.tenant_name}</TableCell>
                    <TableCell>
                      <span className={`font-medium ${CalendarHealthService.getHealthScoreColor(tenant.health_score)}`}>
                        {tenant.health_score} {tenant.health_score >= 90 ? '🟢' : tenant.health_score >= 70 ? '🟡' : '🔴'}
                      </span>
                    </TableCell>
                    <TableCell>{tenant.providers_connected}/{tenant.total_providers}</TableCell>
                    <TableCell>{CalendarHealthService.formatTimeAgo(tenant.last_sync)}</TableCell>
                    <TableCell>
                      {tenant.issues.length === 0 ? (
                        <span className="text-green-600">None</span>
                      ) : (
                        <span className="text-red-600">{tenant.issues.join(', ')}</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
