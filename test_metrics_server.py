#!/usr/bin/env python3
"""
Minimal test server for metrics smoke testing.

This server includes only the essential components needed to test
the metrics functionality without the problematic imports.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(__file__))

# Ensure .env is loaded
from dotenv import load_dotenv
load_dotenv(override=True)

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response, JSONResponse
import logging

# Import metrics components
from backend.middleware.metrics_middleware import CalendarMetricsMiddleware
from backend.metrics import get_metrics_output

# Import only the calendar router (avoiding problematic imports)
try:
    from backend.api.routes.calendar import router as calendar_router
    CALENDAR_ROUTER_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import calendar router: {e}")
    CALENDAR_ROUTER_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO, 
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def create_minimal_app():
    """Create a minimal FastAPI app for metrics testing."""
    app = FastAPI(
        title="Metrics Test Server",
        description="Minimal server for testing calendar metrics",
        version="1.0.0",
    )

    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add metrics middleware for calendar routes
    app.add_middleware(CalendarMetricsMiddleware)

    # Add calendar router if available
    if CALENDAR_ROUTER_AVAILABLE:
        app.include_router(calendar_router)
        logger.info("Calendar router included")
    else:
        # Create a minimal calendar endpoint for testing
        @app.get("/calendar/providers")
        async def get_calendar_providers():
            """Minimal calendar endpoint for testing metrics."""
            return JSONResponse({
                "providers": ["google", "microsoft", "calendly"],
                "message": "Metrics test endpoint"
            })
        
        @app.get("/calendar/connect")
        async def calendar_connect(provider: str = "google"):
            """Minimal calendar connect endpoint for testing metrics."""
            return JSONResponse({
                "provider": provider,
                "status": "test_success",
                "message": f"Test connection to {provider}"
            })
        
        logger.info("Using minimal calendar endpoints for testing")

    # Add metrics endpoint
    @app.get("/metrics")
    async def metrics():
        """Prometheus metrics endpoint."""
        try:
            metrics_output = get_metrics_output()
            return Response(
                content=metrics_output,
                media_type="text/plain; version=0.0.4"
            )
        except Exception as e:
            logger.error(f"Error generating metrics: {e}")
            return Response(
                content=f"Error generating metrics: {e}",
                status_code=500
            )

    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return JSONResponse({"status": "healthy", "service": "metrics-test"})

    # Add startup event
    @app.on_event("startup")
    async def startup_event():
        logger.info("Starting Metrics Test Server")
        logger.info(f"Calendar router available: {CALENDAR_ROUTER_AVAILABLE}")

    return app


# Create the app instance
app = create_minimal_app()

if __name__ == "__main__":
    import uvicorn
    
    print("Starting minimal metrics test server...")
    print("Available endpoints:")
    print("  GET /health - Health check")
    print("  GET /metrics - Prometheus metrics")
    print("  GET /calendar/providers - Calendar providers (test endpoint)")
    print("  GET /calendar/connect?provider=google - Calendar connect (test endpoint)")
    print()
    
    uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")
